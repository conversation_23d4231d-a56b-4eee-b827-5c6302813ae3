# GEMINI.md

## Project Overview

This is a web application for Finanze.Pro. It is a [React](https://react.dev/) project built with [Vite](https://vitejs.dev/) and written in [TypeScript](https://www.typescriptlang.org/). The project uses [TanStack Router](https://tanstack.com/router) for routing and [Tailwind CSS](https://tailwindcss.com/) for styling. The application fetches data from a backend API that is expected to be running on `http://127.0.0.1:5000`.

## Building and Running

### Prerequisites

- [Node.js](https://nodejs.org/) (version specified in `.nvmrc`)
- [pnpm](https://pnpm.io/)

### Installation

```bash
pnpm install
```

### Development

To start the development server, run:

```bash
pnpm dev
```

The application will be available at [http://localhost:5173](http://localhost:5173).

### Building

To build the application for production, run:

```bash
pnpm build
```

The production-ready files will be located in the `dist` directory.

### Testing

To run the tests, use:

```bash
pnpm test
```

## Development Conventions

### Linting

The project uses [ESLint](https://eslint.org/) for code linting. To check the code for linting errors, run:

```bash
pnpm lint
```

### Code Style

The project uses [Prettier](https://prettier.io/) for code formatting. It is recommended to set up your editor to format the code on save.

### Routing

The project uses [TanStack Router](https://tanstack.com/router) for routing. The routes are defined in the `src/routes` directory. The `routeTree.gen.ts` file is generated automatically.

### State Management

The project uses [Zustand](https://zustand-demo.pmnd.rs/) for state management and [TanStack Query](https://tanstack.com/query) for server-state management.

### API

The API client is located in `src/api`. It is generated from an OpenAPI specification. The API requests are proxied to `http://127.0.0.1:5000` in development.
