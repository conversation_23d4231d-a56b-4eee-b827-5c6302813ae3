# Finanze.Pro Web Application

This is a web application for Finanze.Pro. The goal of this project is to help users track and manage their personal finances.

It is a [React](https://react.dev/) project built with [Vite](https://vitejs.dev/) and written in [TypeScript](https://www.typescriptlang.org/).

The project uses [TanStack Router](https://tanstack.com/router) for routing and [Tailwind CSS](https://tailwindcss.com/) for styling. The application fetches data from a backend API that is expected to be running on `http://127.0.0.1:5000`.

## Prerequisites

- [Node.js](https://nodejs.org/) (version specified in `.nvmrc`)
- [pnpm](https://pnpm.io/)

## Setup

Install the dependencies:

```bash
pnpm install
```

## Development

To start the development server, run:

```bash
pnpm dev
```

The application will be available at [http://localhost:5173](http://localhost:5173).

## Building

To build the application for production, run:

```bash
pnpm build
```

The production-ready files will be located in the `dist` directory.

## Testing

To run the tests, use:

```bash
pnpm test
```
