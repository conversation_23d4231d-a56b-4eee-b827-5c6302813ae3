{"name": "webapp", "description": "Finanze.Pro Web Application", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest --run --coverage --coverage.include 'src/**'", "test:ui": "vitest --ui --coverage --coverage.include 'src/**'", "coverage:preview": "vite preview --outDir ./coverage"}, "dependencies": {"@fontsource-variable/inter": "^5.2.6", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "@tanstack/react-router": "^1.131.27", "@tanstack/react-router-devtools": "^1.131.27", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "immer": "^10.1.1", "lucide-react": "^0.540.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@ianvs/prettier-plugin-sort-imports": "^4.6.2", "@tailwindcss/vite": "^4.1.12", "@tanstack/eslint-plugin-query": "^5.83.1", "@tanstack/router-plugin": "^1.131.27", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "vite": "^7.1.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}