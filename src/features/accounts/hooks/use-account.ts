import type { ApiError } from "~/api/client";
import type { Account } from "../types";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useAccount(id: string) {
  return useQuery<Account, ApiError>({
    queryFn: () => apiClient.get(`/v1/accounts/${id}`),
    queryKey: ["accounts", { id }],
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
}
