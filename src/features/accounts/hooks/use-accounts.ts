import type { Account } from "../types";

import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useAccounts() {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<Account[]>("/v1/accounts"),
    queryKey: ["accounts"],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const accounts = useMemo(() => data ?? [], [data]);
  const groups = useMemo(
    () => [...new Set(accounts.map((account) => account.group).filter((group) => group !== null))],
    [accounts]
  );
  const activeAccounts = useMemo(() => accounts.filter((account) => account.is_active), [accounts]);

  return { accounts, groups, activeAccounts, isFetching, isLoading, error };
}
