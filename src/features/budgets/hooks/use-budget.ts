import type { Budget } from "../types";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useBudget(id: string) {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<Budget>(`/v1/budgets/${id}`),
    queryKey: ["budgets", { id }],
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });

  return { budget: data, isFetching, isLoading, error };
}
