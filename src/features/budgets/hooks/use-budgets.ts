import type { Budget } from "../types";

import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useBudgets() {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => apiClient.get<Budget[]>("/v1/budgets"),
    queryKey: ["budgets"],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const budgets = useMemo(() => data ?? [], [data]);
  const activeBudgets = useMemo(() => budgets.filter((budget) => budget.is_active), [budgets]);
  const inactiveBudgets = useMemo(() => budgets.filter((budget) => !budget.is_active), [budgets]);

  return { budgets, activeBudgets, inactiveBudgets, isFetching, isLoading, error };
}
