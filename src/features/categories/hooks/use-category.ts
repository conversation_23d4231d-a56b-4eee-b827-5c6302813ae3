import type { ApiError } from "~/api/client";
import type { Category } from "../types";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

export function useCategory(id: string) {
  return useQuery<Category, ApiError>({
    queryFn: () => apiClient.get(`/v1/categories/${id}`),
    queryKey: ["categories", { id }],
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
}
