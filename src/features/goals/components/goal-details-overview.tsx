import type { Goal } from "../types";

import { useMemo } from "react";

import { ArchiveIcon, CheckCircleIcon as Check<PERSON><PERSON>, EditIcon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import Box from "~/components/blocks/box";
import DefinitionBlock from "~/components/blocks/definition-block";
import { Badge } from "~/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Progress } from "~/components/ui/progress";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatDate } from "~/lib/formatters";

import { useGoalActions } from "../hooks";

interface Props {
  goal: Goal;
}

export default function GoalDetailsOverview({ goal }: Props) {
  const baseCurrency = useBaseCurrency();
  const { editGoal, deleteGoal } = useGoalActions(goal);

  const currentAmount = Number(goal.current_amount);
  const targetAmount = goal.target_amount ? Number(goal.target_amount) : null;
  const progressPercentage = useMemo(() => {
    if (!targetAmount) return 0;
    return Math.min((currentAmount / targetAmount) * 100, 100);
  }, [currentAmount, targetAmount]);

  return (
    <Box className="flex flex-col gap-6 border-t-[6px] ps-8 pe-4 pt-4 pb-8" style={{ borderTopColor: goal.color }}>
      <div className="flex items-center gap-4">
        <p className="text-foreground text-xs/5 font-semibold uppercase" title="Created at">
          {formatDate(goal.created_at)}
        </p>
        <Badge variant="secondary" size="lg">
          Goal
        </Badge>
        {goal.is_active && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <CheckIcon className="size-4" />
            Active
          </p>
        )}
        {!goal.is_active && (
          <p className="text-gray flex items-center gap-1 text-sm/5">
            <ArchiveIcon className="size-4" />
            Inactive
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={editGoal}>
              <EditIcon /> <span className="inline-block pt-0.5">Edit goal</span>
            </DropdownMenuItem>
            <DropdownMenuItem variant="destructive" onClick={deleteGoal}>
              <Trash2Icon /> <span className="inline-block pt-0.5">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">{goal.name}</h2>
        {goal.description && <p className="mt-1 line-clamp-2 text-base text-gray-600">{goal.description}</p>}
      </div>

      <div className="flex gap-12">
        <DefinitionBlock title="Current Amount">
          <p className="flex items-end gap-2">{formatCurrency(baseCurrency, goal.current_amount)}</p>
        </DefinitionBlock>

        {targetAmount && (
          <DefinitionBlock title="Target Amount">
            <p>{formatCurrency(baseCurrency, goal.target_amount!)}</p>
          </DefinitionBlock>
        )}

        {goal.target_date && (
          <DefinitionBlock title="Target Date">
            <p>{formatDate(goal.target_date)}</p>
          </DefinitionBlock>
        )}
      </div>

      {targetAmount && (
        <div className="space-y-4">
          <h3 className="font-medium">Goal Progress</h3>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current / Target</span>
              <span>
                {formatCurrency(baseCurrency, goal.current_amount)} /{" "}
                {formatCurrency(baseCurrency, goal.target_amount!)}
              </span>
            </div>
            <Progress value={progressPercentage} className="h-3" />
            <div className="text-muted-foreground flex justify-between text-xs">
              <span>{Math.min(progressPercentage, 100).toFixed(2)}% achieved</span>
              <span>
                {progressPercentage >= 100
                  ? "Goal achieved!"
                  : progressPercentage >= 75
                    ? "Almost there!"
                    : "Keep going!"}
              </span>
            </div>
          </div>
        </div>
      )}
    </Box>
  );
}
