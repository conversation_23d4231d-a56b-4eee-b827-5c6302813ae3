import type { Goal } from "../types";

import { Link } from "@tanstack/react-router";
import { EditIcon, MoreHorizontalIcon, TrashIcon, ViewIcon } from "lucide-react";

import DefinitionBlock from "~/components/blocks/definition-block";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatPercentage } from "~/lib/formatters";

import { useGoalActions } from "../hooks";

interface Props {
  goal: Goal;
}

export default function GoalsListItem({ goal }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editGoal, deleteGoal } = useGoalActions(goal);

  const targetAmount = goal.target_amount ? Number(goal.target_amount) : null;

  return (
    <div style={{ borderLeftColor: goal.color, borderLeftWidth: 4 }}>
      <div className="bg-card flex items-center justify-between rounded-lg px-4 py-3">
        <div className="flex items-center gap-3">
          <div>
            <Link
              to="/goals/$goalId"
              params={{ goalId: goal.id }}
              className="text-link hover:text-link/90 text-md/6 block flex-grow font-medium hover:underline"
            >
              {goal.name}
            </Link>
            {goal.description && <p className="text-muted-foreground text-sm">{goal.description}</p>}
          </div>
        </div>

        <div className="flex items-center gap-4">
          <DefinitionBlock size="md" title="Current" className="min-w-44">
            {formatCurrency(baseCurrency, goal.current_amount)}
          </DefinitionBlock>

          {targetAmount && (
            <DefinitionBlock size="md" title="Target" className="min-w-44">
              {formatCurrency(baseCurrency, goal.target_amount!)}
            </DefinitionBlock>
          )}

          {goal.target_amount && (
            <DefinitionBlock size="md" title="Progress" className="min-w-24">
              {formatPercentage(
                Math.min(100, (Number(goal.current_amount) / Number(goal.target_amount)) * 100).toFixed(2)
              )}
            </DefinitionBlock>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontalIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to="/goals/$goalId" params={{ goalId: goal.id }}>
                  <ViewIcon />
                  <span className="inline-block pt-0.5">Goal details</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={editGoal}>
                <EditIcon />
                <span className="inline-block pt-0.5">Edit goal</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={deleteGoal} variant="destructive">
                <TrashIcon />
                <span className="inline-block pt-0.5">Delete</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
