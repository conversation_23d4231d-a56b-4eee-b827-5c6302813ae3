import type { Goal } from "../types";

import { PlusIcon } from "lucide-react";

import { Button } from "~/components/ui/button";

import { useGoalActions } from "../hooks";
import GoalsListItem from "./goals-list-item";

interface Props {
  goals: Goal[];
}

export default function GoalsList({ goals }: Props) {
  const { createGoal } = useGoalActions();

  if (goals.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="space-y-4">
          <p className="text-muted-foreground">No goals found</p>
          <Button onClick={() => createGoal()}>
            <PlusIcon />
            <span className="inline-block pt-0.5">Create Your First Goal</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {goals.map((goal) => (
        <GoalsListItem key={goal.id} goal={goal} />
      ))}
    </div>
  );
}
