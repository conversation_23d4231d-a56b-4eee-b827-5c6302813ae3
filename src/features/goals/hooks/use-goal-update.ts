import type { ApiError } from "~/api/client";
import type { Goal, GoalUpdateData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useGoalsStore from "../store";

export function useGoalUpdate(goalId: string) {
  const queryClient = useQueryClient();

  const setDialogOpen = useGoalsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Goal, ApiError, GoalUpdateData>({
    mutationFn: (data) => apiClient.patch(`/v1/goals/${goalId}`, data),
    onSuccess: (goal) => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["goals"] }),
        queryClient.invalidateQueries({ queryKey: ["goals", goalId] }),
      ]);

      toast.success("Goal updated", { description: `Goal ${goal.name} updated successfully.` });

      setDialogOpen(false);
    },
  });
}
