import type { TransactionFilterQuery, TransactionType } from "../types";

import { useMemo } from "react";

import Box from "~/components/blocks/box";
import { MultiSelect } from "~/components/ui/multi-select";
import { useAccounts } from "~/features/accounts/hooks";
import { useCategories } from "~/features/categories/hooks";

interface Props {
  filters: TransactionFilterQuery;
  onFiltersChange: (filters: TransactionFilterQuery) => void;
}

export default function TransactionFilters({ filters, onFiltersChange }: Props) {
  const { activeAccounts, isLoading: isLoadingAccounts } = useAccounts();
  const { categories, isLoading: isLoadingCategories } = useCategories();

  // Transform accounts to multi-select options
  const accountOptions = useMemo(
    () =>
      activeAccounts.map((account) => ({
        label: account.name,
        value: account.id,
      })),
    [activeAccounts]
  );

  // Transform categories to multi-select options
  const categoryOptions = useMemo(
    () =>
      categories.map((category) => ({
        label: category.name,
        value: category.id,
      })),
    [categories]
  );

  // Transaction type options
  const transactionTypeOptions = useMemo(
    () => [
      { label: "Income", value: "income" },
      { label: "Expense", value: "expense" },
      { label: "Transfer", value: "transfer" },
    ],
    []
  );

  // Parse current filter values
  const selectedAccountIds = useMemo(
    () => (filters.account_id ? filters.account_id.split(",") : []),
    [filters.account_id]
  );

  const selectedCategoryIds = useMemo(
    () => (filters.category_id ? filters.category_id.split(",") : []),
    [filters.category_id]
  );

  const selectedTransactionTypes = useMemo(
    () => (filters.transaction_type ? filters.transaction_type.split(",") : []),
    [filters.transaction_type]
  );

  // Handle filter changes
  const handleAccountsChange = (accountIds: string[]) => {
    onFiltersChange({
      ...filters,
      account_id: accountIds.length > 0 ? accountIds.join(",") : undefined,
    });
  };

  const handleCategoriesChange = (categoryIds: string[]) => {
    onFiltersChange({
      ...filters,
      category_id: categoryIds.length > 0 ? categoryIds.join(",") : undefined,
    });
  };

  const handleTransactionTypesChange = (types: string[]) => {
    onFiltersChange({
      ...filters,
      transaction_type: types.length > 0 ? types.join(",") : undefined,
    });
  };

  return (
    <Box className="p-4">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="space-y-2">
          <label className="text-sm font-medium">Accounts</label>
          <MultiSelect
            key={`accounts-${selectedAccountIds.join(",")}`}
            options={accountOptions}
            onValueChange={handleAccountsChange}
            defaultValue={selectedAccountIds}
            placeholder="Select accounts..."
            disabled={isLoadingAccounts}
            modalPopover
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Categories</label>
          <MultiSelect
            key={`categories-${selectedCategoryIds.join(",")}`}
            options={categoryOptions}
            onValueChange={handleCategoriesChange}
            defaultValue={selectedCategoryIds}
            placeholder="Select categories..."
            disabled={isLoadingCategories}
            modalPopover
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Transaction Type</label>
          <MultiSelect
            key={`types-${selectedTransactionTypes.join(",")}`}
            options={transactionTypeOptions}
            onValueChange={handleTransactionTypesChange}
            defaultValue={selectedTransactionTypes}
            placeholder="Select types..."
            modalPopover
          />
        </div>
      </div>
    </Box>
  );
}
