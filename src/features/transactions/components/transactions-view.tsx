import type { TransactionFilterQuery } from "../types";

import { useCallback, useState } from "react";

import PaginationBlock from "~/components/blocks/pagination-block";
import LoadingIndicator from "~/components/elements/loading-indicator";

import { useTransactions } from "../hooks";
import TransactionFilters from "./transaction-filters";
import TransactionsList from "./transactions-list";

export default function TransactionsView() {
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<TransactionFilterQuery>({});

  const { transactions, pagination, isLoading } = useTransactions({ page, ...filters });

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage >= 1 && (!pagination || newPage <= pagination.pages)) {
        setPage(newPage);
      }
    },
    [pagination]
  );

  const handleFiltersChange = useCallback((newFilters: TransactionFilterQuery) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  }, []);

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-4">
      <TransactionFilters filters={filters} onFiltersChange={handleFiltersChange} />

      <TransactionsList transactions={transactions} />

      {pagination && <PaginationBlock pagination={pagination} currentPage={page} onPageChange={handlePageChange} />}
    </div>
  );
}
