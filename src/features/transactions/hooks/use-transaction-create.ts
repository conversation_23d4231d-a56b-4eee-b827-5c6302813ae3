import type { ApiError } from "~/api/client";
import type { Transaction, TransactionData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useTransactionsStore from "../store";

export function useTransactionCreate() {
  const queryClient = useQueryClient();

  const setDialogOpen = useTransactionsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Transaction, ApiError, TransactionData>({
    mutationFn: (data) => apiClient.post("/v1/transactions", data),
    onSuccess: (transaction) => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["transactions"] }),
        queryClient.invalidateQueries({ queryKey: ["accounts"] }),
        queryClient.invalidateQueries({ queryKey: ["stats"] }),
        queryClient.invalidateQueries({ queryKey: ["budgets"] }),
        queryClient.invalidateQueries({ queryKey: ["goals"] }),
      ]);

      toast.success("Transaction created", {
        description: `Transaction for ${transaction.amount} created successfully.`,
      });

      setDialogOpen(false);
    },
  });
}
