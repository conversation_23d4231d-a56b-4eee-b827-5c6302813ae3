import type { TransactionFilterQuery, TransactionPaginationQuery, TransactionsResponse } from "../types";

import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { apiClient } from "~/api";

type UseTransactionsParams = TransactionPaginationQuery & TransactionFilterQuery;

export function useTransactions({
  page = 1,
  per_page = 10,
  pagination_type = "count",
  ...filterQuery
}: UseTransactionsParams = {}) {
  const { data, isFetching, isLoading, error } = useQuery({
    queryFn: () => {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        per_page: per_page.toString(),
        pagination_type,
      });

      Object.entries(filterQuery)
        .filter(([, value]) => value != null && value !== "")
        .forEach(([key, value]) => {
          queryParams.append(key, value);
        });

      return apiClient.get<TransactionsResponse>(`/v1/transactions?${queryParams.toString()}`);
    },
    queryKey: ["transactions", { page, per_page, pagination_type, ...filterQuery }],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const transactions = useMemo(() => data?.items ?? [], [data]);
  const pagination = useMemo(() => data?.pagination, [data]);

  // Group transactions by type
  const incomeTransactions = useMemo(
    () => transactions.filter((transaction) => transaction.transaction_type === "income"),
    [transactions]
  );

  const expenseTransactions = useMemo(
    () => transactions.filter((transaction) => transaction.transaction_type === "expense"),
    [transactions]
  );

  const transferTransactions = useMemo(
    () => transactions.filter((transaction) => transaction.transaction_type === "transfer"),
    [transactions]
  );

  return {
    transactions,
    incomeTransactions,
    expenseTransactions,
    transferTransactions,
    pagination,
    isFetching,
    isLoading,
    error,
  };
}
