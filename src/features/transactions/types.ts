import type { Account } from "~/features/accounts/types";
import type { Category } from "~/features/categories/types";
import type { PaginatedResponse } from "~/types";

import { z } from "zod";

import { TransactionRequestSchema } from "./schemas";

export interface TransactionPaginationQuery {
  // Page number (1-based)
  page?: number;

  // Items per page (count pagination) or days per page (day pagination, max 14)
  per_page?: number;

  // Pagination type: 'count' for traditional pagination, 'day' for day-based pagination
  pagination_type?: "count" | "day";
}

export interface TransactionFilterQuery {
  // Filter by account ID(s) (includes both source and destination accounts). Can be a single UUID or comma-separated list of UUIDs
  account_id?: string;

  // Filter by category ID(s). Can be a single UUID or comma-separated list of UUIDs
  category_id?: string;

  // Filter by transaction type(s). Can be a single type or comma-separated list of types
  transaction_type?: string;

  // Filter transactions for a specific date (ISO format: YYYY-MM-DD)
  date?: string;

  // Filter transactions from this date onwards (ISO format: YYYY-MM-DD)
  date_from?: string;

  // Filter transactions up to this date (ISO format: YYYY-MM-DD)
  date_to?: string;
}

export type TransactionType = "income" | "expense" | "transfer";

export interface Transaction {
  id: string;
  transaction_type: TransactionType;
  transaction_date: string;
  description: string | null;

  category_id: string | null;
  category: Category | null;

  account_id: string;
  account: Account;
  amount: string;
  base_amount: string;

  account_to_id: string | null;
  account_to: Account | null;
  amount_to: string;
  base_amount_to: string;

  created_at: string;
  updated_at: string;
}

export type TransactionData = z.infer<typeof TransactionRequestSchema>;

export type TransactionsResponse = PaginatedResponse<Transaction>;
