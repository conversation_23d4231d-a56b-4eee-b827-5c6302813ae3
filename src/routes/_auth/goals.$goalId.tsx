import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import GoalDetailsOverview from "~/features/goals/components/goal-details-overview";
import GoalDialog from "~/features/goals/components/goal-dialog";
import { useGoal } from "~/features/goals/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { goalId } = Route.useParams();
  const { data: goal, isLoading, error } = useGoal(goalId);

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (error) {
    return <ErrorMessage title="Failed to load goal" error={error} />;
  }

  if (!goal) {
    return <ErrorMessage title="Goal not found" error={{ message: "The requested goal could not be found." }} />;
  }

  return (
    <>
      <PageHeader title="Goal Details" backLink={{ to: "/goals" }} />

      <div className="space-y-6">
        <GoalDetailsOverview goal={goal} />
      </div>

      <GoalDialog />
    </>
  );
}
